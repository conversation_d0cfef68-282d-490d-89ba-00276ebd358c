# Minimale Version des Excel zu CSV Konverters

Write-Host "Excel zu CSV Konverter - Minimal" -ForegroundColor Cyan

# Datei-Dialog
Add-Type -AssemblyName System.Windows.Forms
$fileDialog = New-Object System.Windows.Forms.OpenFileDialog
$fileDialog.Title = "Excel-Datei auswählen"
$fileDialog.Filter = "Excel-Dateien (*.xlsx;*.xls)|*.xlsx;*.xls"

if ($fileDialog.ShowDialog() -ne [System.Windows.Forms.DialogResult]::OK) {
    Write-Host "Abgebrochen." -ForegroundColor Yellow
    exit
}

$sourceFile = $fileDialog.FileName
$fileInfo = Get-Item $sourceFile
$targetFolder = $fileInfo.DirectoryName
$csvPath = Join-Path $targetFolder "$($fileInfo.BaseName).csv"

Write-Host "Konvertiere: $($fileInfo.Name)" -ForegroundColor White
Write-Host "Ziel: $csvPath" -ForegroundColor Gray

$excel = $null
$workbook = $null

try {
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open($sourceFile)
    $worksheet = $workbook.ActiveSheet
    
    # Als CSV UTF-8 speichern
    $worksheet.SaveAs($csvPath, 62)
    
    $workbook.Close($false)
    
    Write-Host "Erfolgreich konvertiert!" -ForegroundColor Green
}
catch {
    Write-Host "Fehler: $($_.Exception.Message)" -ForegroundColor Red
}

# Aufräumen
if ($workbook) { try { $workbook.Close($false) } catch { } }
if ($excel) { 
    try { 
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    } catch { }
}

Write-Host "Fertig!" -ForegroundColor Green
