#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Import Skript für Bestandsdaten in die SQLite3 Datenbank.
Erstellt die Tabelle 'bestand', falls sie nicht existiert, und importiert
Daten aus allen CSV-Dateien in einem angegebenen Verzeichnis.
"""

import sqlite3
import csv
import os
import sys
import glob
from typing import Optional

# --- Konfiguration ---
DB_PATH = r"D:\my_ai\4-Lapp\SFM-Electron\database\sfm_dashboard.db"
CSV_DIR = r"C:\Users\<USER>\OneDrive\Desktop\Lapp\Bestand\240_Bestand\csv"
TABLE_NAME = "auslastung240"
# Erwartete Spaltennamen in den CSV-Dateien (ohne 'Änderung')
CSV_COLUMNS = [
    'aufnahmeDatum', 'aufnahmeZeit', 'maxPlaetze', 'auslastung', 'maxA', 'maxB', 'maxC', 'auslastungA', 'auslastungB', 'auslastungC'
]

# --- Hilfsfunktionen ---
def convert_to_float(value: Optional[str]) -> Optional[float]:
    """Konvertiert einen Wert in einen Float, behandelt deutsche Kommas."""
    if value is None or not value.strip():
        return None
    try:
        return float(value.replace(',', '.'))
    except (ValueError, TypeError):
        print(f"Warnung: Ungültiger Zahlenwert '{value}' wird ignoriert.")
        return None

# --- Kernfunktionen ---
def connect_and_prepare_table(db_path: str, table_name: str) -> Optional[sqlite3.Connection]:
    """
    Stellt die Verbindung zur Datenbank her, erstellt das Verzeichnis bei Bedarf
    und erstellt die Tabelle mit einer sauberen Struktur.
    """
    try:
        db_dir = os.path.dirname(db_path)
        os.makedirs(db_dir, exist_ok=True)
        print(f"✓ Datenbankverzeichnis '{db_dir}' ist bereit.")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Klare und direkte CREATE TABLE Anweisung (ohne "Änderung")
        create_table_sql = f'''
            CREATE TABLE IF NOT EXISTS "{table_name}" (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                "aufnahmeDatum" TEXT,
                "aufnahmeZeit" TEXT,
                "maxPlaetze" TEXT,
                "auslastung" TEXT,
                "maxA" TEXT,
                "maxB" TEXT,
                "maxC" TEXT,
                "auslastungA" TEXT,
                "auslastungB" TEXT,
                "auslastungC" TEXT,
                import_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''
        cursor.execute(create_table_sql)
        print(f"✓ Tabelle '{table_name}' ist bereit.")

        conn.commit()
        return conn
    except (sqlite3.Error, OSError) as e:
        print(f"✗ Datenbank- oder Dateisystemfehler: {e}")
        return None

def import_csv_data(conn: sqlite3.Connection, table_name: str, csv_dir: str):
    """Importiert Daten aus allen CSV-Dateien im angegebenen Verzeichnis."""
    cursor = conn.cursor()
    total_import_count = 0
    total_error_count = 0

    csv_files = glob.glob(os.path.join(csv_dir, '*.csv'))
    if not csv_files:
        print(f"Warnung: Keine CSV-Dateien im Verzeichnis '{csv_dir}' gefunden.")
        return

    print(f"Gefundene CSV-Dateien: {len(csv_files)}")

    # SQL-Statement vorbereiten
    quoted_columns = ', '.join([f'"{col}"' for col in CSV_COLUMNS])
    placeholders = ', '.join(['?'] * len(CSV_COLUMNS))
    sql_insert = f'INSERT INTO "{table_name}" ({quoted_columns}) VALUES ({placeholders})'

    for csv_file_path in csv_files:
        print(f"\n--- Importiere '{os.path.basename(csv_file_path)}' ---")
        import_count = 0
        error_count = 0
        try:
            with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile, delimiter=',')

                # Überprüfen, ob die CSV-Datei Spalten hat
                if not reader.fieldnames:
                    print(f"✗ Warnung: Die CSV-Datei '{os.path.basename(csv_file_path)}' ist leer oder hat keine Spalten. Datei wird übersprungen.")
                    total_error_count += 1
                    continue

                # Überprüfen, ob alle erwarteten Spalten vorhanden sind
                if not all(col in reader.fieldnames for col in CSV_COLUMNS):
                    missing_cols = [col for col in CSV_COLUMNS if col not in reader.fieldnames]
                    print(f"✗ Fehler: Die CSV-Datei '{os.path.basename(csv_file_path)}' enthält nicht alle erwarteten Spalten.")
                    print(f"  Fehlende Spalten: {missing_cols}")
                    total_error_count += 1
                    continue

                for row_num, row in enumerate(reader, start=2):
                    try:
                        values_list = [row.get(col) for col in CSV_COLUMNS]
                        
                        bestand_index = CSV_COLUMNS.index('maxPlaetze')
                        values_list[bestand_index] = convert_to_float(values_list[bestand_index])
                        
                        cursor.execute(sql_insert, tuple(values_list))
                        import_count += 1

                    except sqlite3.Error as e:
                        error_count += 1
                        print(f"✗ SQL-Fehler in Zeile {row_num}: {e} (Daten: {row})")
                    except Exception as e:
                        error_count += 1
                        print(f"✗ Allgemeiner Fehler in Zeile {row_num}: {e} (Daten: {row})")

            conn.commit()
            print(f"✓ Import für Datei abgeschlossen: {import_count} Zeilen importiert, {error_count} Fehler.")
            total_import_count += import_count
            total_error_count += error_count

        except Exception as e:
            print(f"✗ Kritischer Fehler beim Verarbeiten der Datei '{os.path.basename(csv_file_path)}': {e}")
            total_error_count += 1

    print(f"\n\n✓ Gesamtimport abgeschlossen: {total_import_count} Zeilen importiert, {total_error_count} Fehler.")

def main():
    """Hauptfunktion des Skripts."""
    print("🚀 Bestandsimport-Skript wird gestartet...")
    print(f"🗄️  Zieldatenbank: {DB_PATH}")
    print(f"→ Zieltabelle: {TABLE_NAME}")
    print(f"📁 CSV-Verzeichnis: {CSV_DIR}")

    if not os.path.isdir(CSV_DIR):
        print(f"✗ Fehler: Das CSV-Verzeichnis '{CSV_DIR}' existiert nicht.")
        sys.exit(1)

    conn = connect_and_prepare_table(DB_PATH, TABLE_NAME)
    if not conn:
        print("✗ Datenbank-Setup fehlgeschlagen.")
        sys.exit(1)

    try:
        import_csv_data(conn, TABLE_NAME, CSV_DIR)
        print(f"\n✅ Bestandsimport erfolgreich abgeschlossen!")
    finally:
        conn.close()
        print("\n🔐 Datenbankverbindung geschlossen.")

if __name__ == "__main__":
    main()
