# PowerShell Script zum Konvertieren einer einzelnen Excel-Datei zu CSV UTF-8
# Speichert eine ausgewählte .xlsx oder .xls Datei als CSV (UTF-8, durch Trennzeichen getrennt)

param(
    [string]$SourceFile = "",
    [string]$TargetFolder = "",
    [switch]$KeepOriginal = $true
)

function Convert-SingleExcelToCSV {
    param(
        [string]$sourceFile,
        [string]$targetFolder,
        [bool]$keepOriginal
    )

    $success = $false
    $excel = $null
    $workbook = $null

    # Excel-Anwendung starten
    try {
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        Write-Host "Excel-Anwendung gestartet..." -ForegroundColor Green
    }
    catch {
        Write-Host "Fehler: Excel konnte nicht gestartet werden. Ist Microsoft Excel installiert?" -ForegroundColor Red
        return
    }

    # Quelldatei validieren
    if (-not (Test-Path $sourceFile)) {
        Write-Host "Fehler: Quelldatei existiert nicht: $sourceFile" -ForegroundColor Red
        if ($excel) { $excel.Quit() }
        return
    }

    # Zielordner validieren/erstellen
    if (-not (Test-Path $targetFolder)) {
        try {
            New-Item -Path $targetFolder -ItemType Directory -Force
            Write-Host "Zielordner erstellt: $targetFolder" -ForegroundColor Yellow
        }
        catch {
            Write-Host "Fehler: Zielordner konnte nicht erstellt werden: $targetFolder" -ForegroundColor Red
            if ($excel) { $excel.Quit() }
            return
        }
    }

    # Dateiinformationen
    $fileInfo = Get-Item $sourceFile
    $fileName = $fileInfo.BaseName
    $csvFileName = "$fileName.csv"
    $csvPath = Join-Path $targetFolder $csvFileName

    Write-Host "`nQuelldatei: $($fileInfo.Name)" -ForegroundColor Cyan
    Write-Host "Zielordner: $targetFolder" -ForegroundColor Gray
    Write-Host "CSV-Dateiname: $csvFileName" -ForegroundColor Gray
    Write-Host "Original-Datei behalten: $keepOriginal" -ForegroundColor Gray
    Write-Host "`n" + "="*60

    try {
        Write-Host "Konvertiere: $($fileInfo.Name)" -ForegroundColor White

        # Excel-Datei öffnen
        $workbook = $excel.Workbooks.Open($fileInfo.FullName)
        $worksheet = $workbook.ActiveSheet

        # Informationen über die Arbeitsmappe anzeigen
        $lastRow = $worksheet.UsedRange.Rows.Count
        $lastCol = $worksheet.UsedRange.Columns.Count
        Write-Host "  Arbeitsblatt: $($worksheet.Name)" -ForegroundColor Gray
        Write-Host "  Zeilen: $lastRow, Spalten: $lastCol" -ForegroundColor Gray

        # Als CSV UTF-8 speichern (xlCSVUTF8 = 62)
        $worksheet.SaveAs($csvPath, 62)

        # Arbeitsmappe schließen
        $workbook.Close($false)
        $workbook = $null

        Write-Host "  ✓ Erfolgreich gespeichert: $csvFileName" -ForegroundColor Green

        # Original-Datei löschen falls gewünscht
        if (-not $keepOriginal) {
            try {
                Remove-Item $fileInfo.FullName -Force
                Write-Host "  ✓ Original-Datei gelöscht" -ForegroundColor Yellow
            }
            catch {
                Write-Host "  ⚠ Warnung: Original-Datei konnte nicht gelöscht werden: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        $success = $true
    }
    catch {
        Write-Host "  ✗ Fehler bei Datei $($fileInfo.Name): $($_.Exception.Message)" -ForegroundColor Red

        # Falls Arbeitsmappe noch offen ist, versuchen zu schließen
        if ($workbook) {
            try {
                $workbook.Close($false)
            }
            catch { }
        }

        $success = $false
    }
    finally {
        # Excel beenden
        if ($excel) {
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
    }

    # Zusammenfassung
    Write-Host "`n" + "="*60
    if ($success) {
        Write-Host "KONVERTIERUNG ERFOLGREICH" -ForegroundColor Green
        Write-Host "CSV-Datei wurde gespeichert:" -ForegroundColor Green
        Write-Host "$csvPath" -ForegroundColor Gray
    } else {
        Write-Host "KONVERTIERUNG FEHLGESCHLAGEN" -ForegroundColor Red
    }
}

# Hauptfunktion mit Dialog-Auswahl
function Start-SingleFileConversionWithDialog {
    Write-Host "Excel zu CSV Konverter (Einzeldatei)" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    
    # Quelldatei auswählen
    if ([string]::IsNullOrEmpty($SourceFile)) {
        Add-Type -AssemblyName System.Windows.Forms
        $fileDialog = New-Object System.Windows.Forms.OpenFileDialog
        $fileDialog.Title = "Excel-Datei auswählen"
        $fileDialog.Filter = "Excel-Dateien (*.xlsx;*.xls)|*.xlsx;*.xls|Alle Dateien (*.*)|*.*"
        $fileDialog.FilterIndex = 1
        $fileDialog.RestoreDirectory = $true
        
        if ($fileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $SourceFile = $fileDialog.FileName
        } else {
            Write-Host "Abgebrochen: Keine Datei ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Zielordner auswählen
    if ([string]::IsNullOrEmpty($TargetFolder)) {
        $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowser.Description = "Zielordner für CSV-Datei auswählen"
        $folderBrowser.RootFolder = [System.Environment+SpecialFolder]::Desktop
        
        if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $TargetFolder = $folderBrowser.SelectedPath
        } else {
            Write-Host "Abgebrochen: Kein Zielordner ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Bestätigung für Original-Datei
    if (-not $PSBoundParameters.ContainsKey('KeepOriginal')) {
        $choice = Read-Host "`nOriginal Excel-Datei behalten? (J/N, Standard: J)"
        $KeepOriginal = ($choice -eq "" -or $choice -match "^[Jj]")
    }
    
    # Konvertierung starten
    Convert-SingleExcelToCSV -sourceFile $SourceFile -targetFolder $TargetFolder -keepOriginal $KeepOriginal
}

# Einfache Version mit festen Pfaden
function Convert-SingleExcelToCSVSimple {
    param(
        [string]$SourceFilePath = "C:\Users\<USER>\OneDrive\Desktop\Lapp\bewegungen\ATrL-klein.xlsx",      # HIER ANPASSEN
        [string]$TargetPath = "C:\Users\<USER>\OneDrive\Desktop\Lapp\bewegungen"                       # HIER ANPASSEN
    )
    
    Convert-SingleExcelToCSV -sourceFile $SourceFilePath -targetFolder $TargetPath -keepOriginal $true
}

# Version die automatisch neben der Quelldatei speichert
function Convert-ExcelToCSVInPlace {
    Add-Type -AssemblyName System.Windows.Forms
    
    # Dateiauswahl
    $fileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $fileDialog.Title = "Excel-Datei auswählen"
    $fileDialog.Filter = "Excel-Dateien (*.xlsx;*.xls)|*.xlsx;*.xls"
    
    if ($fileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $sourceFile = $fileDialog.FileName
        $sourceDirectory = Split-Path $sourceFile -Parent
        
        Write-Host "Konvertiere Datei im gleichen Ordner..." -ForegroundColor Cyan
        Convert-SingleExcelToCSV -sourceFile $sourceFile -targetFolder $sourceDirectory -keepOriginal $true
    } else {
        Write-Host "Keine Datei ausgewählt." -ForegroundColor Yellow
    }
}

# Version mit Drag & Drop Simulation (Pfad als Parameter)
function Convert-ExcelFromPath {
    param(
        [Parameter(Mandatory=$true)]
        [string]$FilePath
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "Datei nicht gefunden: $FilePath" -ForegroundColor Red
        return
    }
    
    $fileInfo = Get-Item $FilePath
    if ($fileInfo.Extension -notin @('.xlsx', '.xls')) {
        Write-Host "Keine Excel-Datei: $FilePath" -ForegroundColor Red
        return
    }
    
    $targetFolder = Split-Path $FilePath -Parent
    Write-Host "Konvertiere: $($fileInfo.Name)" -ForegroundColor Cyan
    Convert-SingleExcelToCSV -sourceFile $FilePath -targetFolder $targetFolder -keepOriginal $true
}

# Script ausführen
if ($SourceFile -ne "" -and $TargetFolder -ne "") {
    # Direkte Ausführung mit Parametern
    Convert-SingleExcelToCSV -sourceFile $SourceFile -targetFolder $TargetFolder -keepOriginal $KeepOriginal
} else {
    # Interaktive Ausführung
    Start-SingleFileConversionWithDialog
}

<#
VERWENDUNG:

1. INTERAKTIV (mit Dialog):
   .\ConvertSingleExcelToCSV.ps1

2. MIT PARAMETERN:
   .\ConvertSingleExcelToCSV.ps1 -SourceFile "C:\Pfad\Datei.xlsx" -TargetFolder "C:\CSV\Ordner"

3. ORIGINAL-DATEI LÖSCHEN:
   .\ConvertSingleExcelToCSV.ps1 -SourceFile "C:\Pfad\Datei.xlsx" -TargetFolder "C:\CSV\Ordner" -KeepOriginal:$false

4. EINFACHE VERSION (Pfade im Script anpassen):
   Convert-SingleExcelToCSVSimple

5. IM GLEICHEN ORDNER SPEICHERN:
   Convert-ExcelToCSVInPlace

6. DIREKT MIT DATEIPFAD:
   Convert-ExcelFromPath -FilePath "C:\Pfad\zu\Datei.xlsx"

FEATURES:
- Konvertiert eine einzelne .xlsx oder .xls Datei
- UTF-8 CSV Format mit Trennzeichen
- Dateiauswahl-Dialog
- Detaillierte Informationen über die Arbeitsmappe
- Fehlerbehandlung
- Optional: Original-Datei löschen
- Validierung der Datei- und Ordnerpfade
- Mehrere Verwendungsmöglichkeiten
- Zeigt Zeilen- und Spaltenanzahl an
#>