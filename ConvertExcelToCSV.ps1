# PowerShell Script zum Konvertieren aller Excel-Dateien zu CSV UTF-8
# Speichert alle .xlsx und .xls Dateien als CSV (UTF-8, durch Trennzeichen getrennt)

param(
    [string]$SourceFolder = "",
    [string]$TargetFolder = "",
    [switch]$KeepOriginal = $true
)

function Convert-ExcelToCSV {
    param(
        [string]$sourceFolder,
        [string]$targetFolder,
        [bool]$keepOriginal
    )
    
    # Excel-Anwendung starten
    try {
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        Write-Host "Excel-Anwendung gestartet..." -ForegroundColor Green
    }
    catch {
        Write-Host "Fehler: Excel konnte nicht gestartet werden. Ist Microsoft Excel installiert?" -ForegroundColor Red
        return
    }
    
    # Ordnerpfade validieren
    if (-not (Test-Path $sourceFolder)) {
        Write-Host "Fehler: Quellordner existiert nicht: $sourceFolder" -ForegroundColor Red
        $excel.Quit()
        return
    }
    
    if (-not (Test-Path $targetFolder)) {
        try {
            New-Item -Path $targetFolder -ItemType Directory -Force
            Write-Host "Zielordner erstellt: $targetFolder" -ForegroundColor Yellow
        }
        catch {
            Write-Host "Fehler: Zielordner konnte nicht erstellt werden: $targetFolder" -ForegroundColor Red
            $excel.Quit()
            return
        }
    }
    
    # Excel-Dateien finden
    $excelFiles = Get-ChildItem -Path $sourceFolder -Include "*.xlsx", "*.xls" -Recurse -File
    $totalFiles = $excelFiles.Count
    $processedFiles = 0
    $successfulFiles = 0
    
    Write-Host "`nGefundene Excel-Dateien: $totalFiles" -ForegroundColor Cyan
    Write-Host "Quellordner: $sourceFolder" -ForegroundColor Gray
    Write-Host "Zielordner: $targetFolder" -ForegroundColor Gray
    Write-Host "Original-Dateien behalten: $keepOriginal" -ForegroundColor Gray
    Write-Host "`n" + "="*60
    
    foreach ($file in $excelFiles) {
        $processedFiles++
        $fileName = $file.BaseName
        $csvFileName = "$fileName.csv"
        $csvPath = Join-Path $targetFolder $csvFileName
        
        Write-Host "[$processedFiles/$totalFiles] Konvertiere: $($file.Name)" -ForegroundColor White
        
        try {
            # Excel-Datei öffnen
            $workbook = $excel.Workbooks.Open($file.FullName)
            $worksheet = $workbook.ActiveSheet
            
            # Als CSV UTF-8 speichern (xlCSVUTF8 = 62)
            $worksheet.SaveAs($csvPath, 62)
            
            # Arbeitsmappe schließen
            $workbook.Close($false)
            
            $successfulFiles++
            Write-Host "  ✓ Erfolgreich gespeichert: $csvFileName" -ForegroundColor Green
            
            # Original-Datei löschen falls gewünscht
            if (-not $keepOriginal) {
                Remove-Item $file.FullName -Force
                Write-Host "  ✓ Original-Datei gelöscht" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "  ✗ Fehler bei Datei $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
            
            # Falls Arbeitsmappe noch offen ist, versuchen zu schließen
            try {
                if ($workbook) {
                    $workbook.Close($false)
                }
            }
            catch { }
        }
    }
    
    # Excel beenden
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    
    # Zusammenfassung
    Write-Host "`n" + "="*60
    Write-Host "KONVERTIERUNG ABGESCHLOSSEN" -ForegroundColor Cyan
    Write-Host "Verarbeitete Dateien: $processedFiles" -ForegroundColor White
    Write-Host "Erfolgreich konvertiert: $successfulFiles" -ForegroundColor Green
    Write-Host "Fehlgeschlagen: $($processedFiles - $successfulFiles)" -ForegroundColor Red
    
    if ($successfulFiles -gt 0) {
        Write-Host "`nCSV-Dateien wurden im Zielordner gespeichert:" -ForegroundColor Green
        Write-Host "$targetFolder" -ForegroundColor Gray
    }
}

# Hauptfunktion mit Dialog-Auswahl
function Start-ConversionWithDialog {
    Write-Host "Excel zu CSV Konverter" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    
    # Quellordner auswählen
    if ([string]::IsNullOrEmpty($SourceFolder)) {
        Add-Type -AssemblyName System.Windows.Forms
        $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowser.Description = "Quellordner mit Excel-Dateien auswählen"
        $folderBrowser.RootFolder = [System.Environment+SpecialFolder]::Desktop
        
        if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $SourceFolder = $folderBrowser.SelectedPath
        } else {
            Write-Host "Abgebrochen: Kein Quellordner ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Zielordner auswählen
    if ([string]::IsNullOrEmpty($TargetFolder)) {
        $folderBrowser.Description = "Zielordner für CSV-Dateien auswählen"
        
        if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $TargetFolder = $folderBrowser.SelectedPath
        } else {
            Write-Host "Abgebrochen: Kein Zielordner ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Bestätigung für Original-Dateien
    if (-not $PSBoundParameters.ContainsKey('KeepOriginal')) {
        $choice = Read-Host "`nOriginal Excel-Dateien behalten? (J/N, Standard: J)"
        $KeepOriginal = ($choice -eq "" -or $choice -match "^[Jj]")
    }
    
    # Konvertierung starten
    Convert-ExcelToCSV -sourceFolder $SourceFolder -targetFolder $TargetFolder -keepOriginal $KeepOriginal
}

# Einfache Version mit festen Pfaden
function Convert-ExcelToCSVSimple {
    param(
        [string]$SourcePath = "C:\Users\<USER>\OneDrive\Desktop\Lapp\Bestand\240_Bestand",      # HIER ANPASSEN
        [string]$TargetPath = "C:\Users\<USER>\OneDrive\Desktop\Lapp\Bestand\240_Bestand\csv"       # HIER ANPASSEN
    )
    
    Convert-ExcelToCSV -sourceFolder $SourcePath -targetFolder $TargetPath -keepOriginal $true
}

# Script ausführen
if ($SourceFolder -ne "" -and $TargetFolder -ne "") {
    # Direkte Ausführung mit Parametern
    Convert-ExcelToCSV -sourceFolder $SourceFolder -targetFolder $TargetFolder -keepOriginal $KeepOriginal
} else {
    # Interaktive Ausführung
    Start-ConversionWithDialog
}

<#
VERWENDUNG:

1. INTERAKTIV (mit Dialog):
   .\ConvertExcelToCSV.ps1

2. MIT PARAMETERN:
   .\ConvertExcelToCSV.ps1 -SourceFolder "C:\Excel\Dateien" -TargetFolder "C:\CSV\Dateien"

3. ORIGINAL-DATEIEN LÖSCHEN:
   .\ConvertExcelToCSV.ps1 -SourceFolder "C:\Excel\Dateien" -TargetFolder "C:\CSV\Dateien" -KeepOriginal:$false

4. EINFACHE VERSION (Pfade im Script anpassen):
   Convert-ExcelToCSVSimple

FEATURES:
- Konvertiert alle .xlsx und .xls Dateien
- UTF-8 CSV Format mit Trennzeichen
- Fortschrittsanzeige
- Fehlerbehandlung
- Optional: Original-Dateien löschen
- Validierung der Ordnerpfade
- Detaillierte Zusammenfassung
#>