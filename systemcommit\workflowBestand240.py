import os
import re
import sqlite3
import pandas as pd
from datetime import datetime

# ------------------------- CONFIG -------------------------
EXPORT_DIR = r"C:\Users\<USER>\OneDrive\Desktop\Lapp\Database\auslastung240"
DASHBOARD_DB_PATH = r"C:\Users\<USER>\OneDrive\Desktop\Lapp\Database\testdb\sfm_dashboard.db"
BASENAME = "240_Bestand"

MAX_PLAETZE = 11053
MAX_A = 2340
MAX_B = 3564
MAX_C = 4848
# ----------------------------------------------------------

def find_latest_file(folder, base):
    files = [f for f in os.listdir(folder) if f.startswith(base) and f.endswith(".xlsx")]
    if not files:
        return None
    return max([os.path.join(folder, f) for f in files], key=os.path.getmtime)

def extract_datetime(fname):
    # Suche nach "dd.mm.yy_HH.MM.SS"
    m = re.search(r"(\d{2})\.(\d{2})\.(\d{2})_(\d{2})\.(\d{2})\.(\d{2})", fname)
    if m:
        day, month, year, hour, minute, second = m.groups()
        date_obj = datetime.strptime(f"20{year}{month}{day}", "%Y%m%d").date()
        time_obj = datetime.strptime(f"{hour}{minute}{second}", "%H%M%S").time()
        return date_obj, time_obj
    return None, None

def process_and_store(excel_path):
    df = pd.read_excel(excel_path, engine="openpyxl")
    df.columns = [c.replace(".", "_") for c in df.columns]  

    date_part, time_part = extract_datetime(os.path.basename(excel_path))
    df["aufnahmeDatum"] = date_part.strftime("%Y-%m-%d")
    df["aufnahmeZeit"]  = time_part

    df["maxPlaetze"] = MAX_PLAETZE
    df["auslastung"]  = round(len(df) / MAX_PLAETZE * 100, 2)
    df["maxA"] = MAX_A
    df["maxB"] = MAX_B
    df["maxC"] = MAX_C

    aaa_cnt = df.iloc[:, 5].astype(str).str.strip().str.upper().eq("AAA").sum()
    df["auslastungA"] = round(aaa_cnt / MAX_A * 100, 2)
    df["auslastungB"] = round(aaa_cnt / MAX_B * 100, 2)
    df["auslastungC"] = round(aaa_cnt / MAX_C * 100, 2)

    # CSV export
    csv_path = os.path.join(os.path.dirname(excel_path), f"{BASENAME}.csv")
    df.to_csv(csv_path, index=False, sep=",", encoding="utf-8")
    print("CSV gespeichert:", csv_path)

    # SQLite insert
    with sqlite3.connect(DASHBOARD_DB_PATH) as conn:
        df.to_sql("bestand240", conn, if_exists="append", index=False)
    print("Daten in 'bestand240' eingefügt.")

if __name__ == "__main__":
    xl = find_latest_file(EXPORT_DIR, BASENAME)
    if xl:
        print("Verarbeite:", xl)
        process_and_store(xl)
    else:
        print("Keine passende Excel-Datei gefunden.")