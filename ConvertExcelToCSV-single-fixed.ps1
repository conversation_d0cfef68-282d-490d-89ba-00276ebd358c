# PowerShell Script zum Konvertieren einer einzelnen Excel-Datei zu CSV UTF-8
# Speichert eine ausgewählte .xlsx oder .xls Datei als CSV (UTF-8, durch Trennzeichen getrennt)

param(
    [string]$SourceFile = "",
    [string]$TargetFolder = "",
    [switch]$KeepOriginal = $true
)

function Convert-SingleExcelToCSV {
    param(
        [string]$sourceFile,
        [string]$targetFolder,
        [bool]$keepOriginal
    )
    
    $success = $false
    $excel = $null
    $workbook = $null
    
    # Excel-Anwendung starten
    try {
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        Write-Host "Excel-Anwendung gestartet..." -ForegroundColor Green
    }
    catch {
        Write-Host "Fehler: Excel konnte nicht gestartet werden. Ist Microsoft Excel installiert?" -ForegroundColor Red
        return
    }
    
    # Quelldatei validieren
    if (-not (Test-Path $sourceFile)) {
        Write-Host "Fehler: Quelldatei existiert nicht: $sourceFile" -ForegroundColor Red
        if ($excel) { $excel.Quit() }
        return
    }
    
    # Zielordner validieren/erstellen
    if (-not (Test-Path $targetFolder)) {
        try {
            New-Item -Path $targetFolder -ItemType Directory -Force
            Write-Host "Zielordner erstellt: $targetFolder" -ForegroundColor Yellow
        }
        catch {
            Write-Host "Fehler: Zielordner konnte nicht erstellt werden: $targetFolder" -ForegroundColor Red
            if ($excel) { $excel.Quit() }
            return
        }
    }
    
    # Dateiinformationen
    $fileInfo = Get-Item $sourceFile
    $fileName = $fileInfo.BaseName
    $csvFileName = "$fileName.csv"
    $csvPath = Join-Path $targetFolder $csvFileName
    
    Write-Host "`nQuelldatei: $($fileInfo.Name)" -ForegroundColor Cyan
    Write-Host "Zielordner: $targetFolder" -ForegroundColor Gray
    Write-Host "CSV-Dateiname: $csvFileName" -ForegroundColor Gray
    Write-Host "Original-Datei behalten: $keepOriginal" -ForegroundColor Gray
    Write-Host "`n" + "="*60
    
    try {
        Write-Host "Konvertiere: $($fileInfo.Name)" -ForegroundColor White
        
        # Excel-Datei öffnen
        $workbook = $excel.Workbooks.Open($fileInfo.FullName)
        $worksheet = $workbook.ActiveSheet
        
        # Informationen über die Arbeitsmappe anzeigen
        $lastRow = $worksheet.UsedRange.Rows.Count
        $lastCol = $worksheet.UsedRange.Columns.Count
        Write-Host "  Arbeitsblatt: $($worksheet.Name)" -ForegroundColor Gray
        Write-Host "  Zeilen: $lastRow, Spalten: $lastCol" -ForegroundColor Gray
        
        # Als CSV UTF-8 speichern (xlCSVUTF8 = 62)
        $worksheet.SaveAs($csvPath, 62)
        
        # Arbeitsmappe schließen
        $workbook.Close($false)
        $workbook = $null
        
        Write-Host "  ✓ Erfolgreich gespeichert: $csvFileName" -ForegroundColor Green
        
        # Original-Datei löschen falls gewünscht
        if (-not $keepOriginal) {
            try {
                Remove-Item $fileInfo.FullName -Force
                Write-Host "  ✓ Original-Datei gelöscht" -ForegroundColor Yellow
            }
            catch {
                Write-Host "  ⚠ Warnung: Original-Datei konnte nicht gelöscht werden: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        $success = $true
    }
    catch {
        Write-Host "  ✗ Fehler bei Datei $($fileInfo.Name): $($_.Exception.Message)" -ForegroundColor Red
        
        # Falls Arbeitsmappe noch offen ist, versuchen zu schließen
        if ($workbook) {
            try {
                $workbook.Close($false)
            }
            catch { }
        }
        
        $success = $false
    }
    finally {
        # Excel beenden
        if ($excel) {
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
    }
    
    # Zusammenfassung
    Write-Host "`n" + "="*60
    if ($success) {
        Write-Host "KONVERTIERUNG ERFOLGREICH" -ForegroundColor Green
        Write-Host "CSV-Datei wurde gespeichert:" -ForegroundColor Green
        Write-Host "$csvPath" -ForegroundColor Gray
    } else {
        Write-Host "KONVERTIERUNG FEHLGESCHLAGEN" -ForegroundColor Red
    }
}

# Hauptfunktion mit Dialog-Auswahl
function Start-SingleFileConversionWithDialog {
    Write-Host "Excel zu CSV Konverter (Einzeldatei)" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    
    # Quelldatei auswählen
    if ([string]::IsNullOrEmpty($SourceFile)) {
        Add-Type -AssemblyName System.Windows.Forms
        $fileDialog = New-Object System.Windows.Forms.OpenFileDialog
        $fileDialog.Title = "Excel-Datei auswählen"
        $fileDialog.Filter = "Excel-Dateien (*.xlsx;*.xls)|*.xlsx;*.xls|Alle Dateien (*.*)|*.*"
        $fileDialog.FilterIndex = 1
        $fileDialog.RestoreDirectory = $true
        
        if ($fileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $SourceFile = $fileDialog.FileName
        } else {
            Write-Host "Abgebrochen: Keine Datei ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Zielordner auswählen
    if ([string]::IsNullOrEmpty($TargetFolder)) {
        $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowser.Description = "Zielordner für CSV-Datei auswählen"
        $folderBrowser.RootFolder = [System.Environment+SpecialFolder]::Desktop
        
        if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $TargetFolder = $folderBrowser.SelectedPath
        } else {
            Write-Host "Abgebrochen: Kein Zielordner ausgewählt." -ForegroundColor Yellow
            return
        }
    }
    
    # Bestätigung für Original-Datei
    if (-not $PSBoundParameters.ContainsKey('KeepOriginal')) {
        $choice = Read-Host "`nOriginal Excel-Datei behalten? (J/N, Standard: J)"
        $KeepOriginal = ($choice -eq "" -or $choice -match "^[Jj]")
    }
    
    # Konvertierung starten
    Convert-SingleExcelToCSV -sourceFile $SourceFile -targetFolder $TargetFolder -keepOriginal $KeepOriginal
}

# Script ausführen
if ($SourceFile -ne "" -and $TargetFolder -ne "") {
    # Direkte Ausführung mit Parametern
    Convert-SingleExcelToCSV -sourceFile $SourceFile -targetFolder $TargetFolder -keepOriginal $KeepOriginal
} else {
    # Interaktive Ausführung
    Start-SingleFileConversionWithDialog
}
