# PowerShell Script zum Konvertieren einer Excel-Datei zu CSV UTF-8

param(
    [string]$SourceFile = "",
    [string]$TargetFolder = "",
    [switch]$KeepOriginal = $true
)

Write-Host "Excel zu CSV Konverter" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

# Quelldatei auswählen falls nicht angegeben
if ([string]::IsNullOrEmpty($SourceFile)) {
    Add-Type -AssemblyName System.Windows.Forms
    $fileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $fileDialog.Title = "Excel-Datei auswählen"
    $fileDialog.Filter = "Excel-Dateien (*.xlsx;*.xls)|*.xlsx;*.xls|Alle Dateien (*.*)|*.*"
    $fileDialog.FilterIndex = 1
    $fileDialog.RestoreDirectory = $true
    
    if ($fileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $SourceFile = $fileDialog.FileName
    } else {
        Write-Host "Abgebrochen: Keine Datei ausgewählt." -ForegroundColor Yellow
        exit
    }
}

# Zielordner auswählen falls nicht angegeben
if ([string]::IsNullOrEmpty($TargetFolder)) {
    $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog
    $folderBrowser.Description = "Zielordner für CSV-Datei auswählen"
    $folderBrowser.RootFolder = [System.Environment+SpecialFolder]::Desktop
    
    if ($folderBrowser.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $TargetFolder = $folderBrowser.SelectedPath
    } else {
        Write-Host "Abgebrochen: Kein Zielordner ausgewählt." -ForegroundColor Yellow
        exit
    }
}

# Quelldatei validieren
if (-not (Test-Path $SourceFile)) {
    Write-Host "Fehler: Quelldatei existiert nicht: $SourceFile" -ForegroundColor Red
    exit
}

# Zielordner validieren/erstellen
if (-not (Test-Path $TargetFolder)) {
    try {
        New-Item -Path $TargetFolder -ItemType Directory -Force | Out-Null
        Write-Host "Zielordner erstellt: $TargetFolder" -ForegroundColor Yellow
    }
    catch {
        Write-Host "Fehler: Zielordner konnte nicht erstellt werden: $TargetFolder" -ForegroundColor Red
        exit
    }
}

# Dateiinformationen
$fileInfo = Get-Item $SourceFile
$fileName = $fileInfo.BaseName
$csvFileName = "$fileName.csv"
$csvPath = Join-Path $TargetFolder $csvFileName

Write-Host "`nQuelldatei: $($fileInfo.Name)" -ForegroundColor Cyan
Write-Host "Zielordner: $TargetFolder" -ForegroundColor Gray
Write-Host "CSV-Dateiname: $csvFileName" -ForegroundColor Gray
Write-Host "Original-Datei behalten: $KeepOriginal" -ForegroundColor Gray
Write-Host "`n" + "="*60

$excel = $null
$workbook = $null
$success = $false

try {
    # Excel-Anwendung starten
    Write-Host "Starte Excel-Anwendung..." -ForegroundColor Green
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "Konvertiere: $($fileInfo.Name)" -ForegroundColor White
    
    # Excel-Datei öffnen
    $workbook = $excel.Workbooks.Open($fileInfo.FullName)
    $worksheet = $workbook.ActiveSheet
    
    # Informationen über die Arbeitsmappe anzeigen
    $lastRow = $worksheet.UsedRange.Rows.Count
    $lastCol = $worksheet.UsedRange.Columns.Count
    Write-Host "  Arbeitsblatt: $($worksheet.Name)" -ForegroundColor Gray
    Write-Host "  Zeilen: $lastRow, Spalten: $lastCol" -ForegroundColor Gray
    
    # Als CSV UTF-8 speichern (xlCSVUTF8 = 62)
    $worksheet.SaveAs($csvPath, 62)
    
    # Arbeitsmappe schließen
    $workbook.Close($false)
    $workbook = $null
    
    Write-Host "  ✓ Erfolgreich gespeichert: $csvFileName" -ForegroundColor Green
    
    # Original-Datei löschen falls gewünscht
    if (-not $KeepOriginal) {
        try {
            Remove-Item $fileInfo.FullName -Force
            Write-Host "  ✓ Original-Datei gelöscht" -ForegroundColor Yellow
        }
        catch {
            Write-Host "  ⚠ Warnung: Original-Datei konnte nicht gelöscht werden: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }

    $success = $true
}
catch {
    Write-Host "  ✗ Fehler: $($_.Exception.Message)" -ForegroundColor Red
    $success = $false
}

# Aufräumen
if ($workbook) {
    try { $workbook.Close($false) } catch { }
}
if ($excel) {
    try {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    } catch { }
}
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

# Zusammenfassung
Write-Host "`n" + "="*60
if ($success) {
    Write-Host "KONVERTIERUNG ERFOLGREICH" -ForegroundColor Green
    Write-Host "CSV-Datei wurde gespeichert:" -ForegroundColor Green
    Write-Host "$csvPath" -ForegroundColor Gray
} else {
    Write-Host "KONVERTIERUNG FEHLGESCHLAGEN" -ForegroundColor Red
}

Write-Host "`nDrücken Sie eine beliebige Taste zum Beenden..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
