import sqlite3
import json

con = sqlite3.connect("stoerungen.db")
cur = con.cursor()

# <PERSON><PERSON><PERSON> anlegen (falls nötig)
cur.execute("""
CREATE TABLE IF NOT EXISTS Stoerungen (
    id              INTEGER  NOT NULL PRIMARY KEY AUTOINCREMENT,
    title           TEXT     NOT NULL,
    description     TEXT,
    severity        TEXT     NOT NULL,
    status          TEXT     NOT NULL,
    category        TEXT,
    affected_system TEXT,
    location        TEXT,
    reported_by     TEXT,
    assigned_to     TEXT,
    created_at      DATETIME NOT NULL,
    updated_at      DATETIME NOT NULL,
    resolved_at     DATETIME,
    mttr_minutes    INTEGER,
    tags            TEXT
);
""")

# JSO<PERSON> e<PERSON>
with open("fakeData.json", encoding="utf-8") as f:
    records = json.load(f)

# Massen-Insert
cur.executemany("""
INSERT INTO Stoerungen(
    title, description, severity, status, category,
    affected_system, location, reported_by, assigned_to,
    created_at, updated_at, resolved_at, mttr_minutes, tags
) VALUES (
    :title, :description, :severity, :status, :category,
    :affected_system, :location, :reported_by, :assigned_to,
    :created_at, :updated_at, :resolved_at, :mttr_minutes, :tags
)
""", records)

con.commit()
con.close()